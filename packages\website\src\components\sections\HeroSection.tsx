import Link from 'next/link';
import { ArrowR<PERSON>, Play, Star, Users, Zap } from 'lucide-react';

export default function HeroSection() {
  const stats = [
    { icon: Users, value: '10K+', label: 'Developers' },
    { icon: Star, value: '4.9/5', label: 'Rating' },
    { icon: Zap, value: '99.9%', label: 'Uptime' },
  ];

  return (
    <section className="relative pt-20 pb-16 sm:pt-24 sm:pb-20 lg:pt-32 lg:pb-28">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"></div>
      
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm font-medium text-blue-800 mb-8">
            <span className="mr-2">🚀</span>
            New: Advanced API Testing Features Available
          </div>

          {/* Main heading */}
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
            The Modern Alternative to{' '}
            <span className="gradient-text">Postman & Apifox</span>
          </h1>

          {/* Subheading */}
          <p className="mx-auto mt-6 max-w-3xl text-lg leading-8 text-gray-600 sm:text-xl">
            Build, test, and document your APIs with our intuitive platform. 
            Experience faster workflows, better collaboration, and comprehensive 
            API management in one powerful tool.
          </p>

          {/* CTA Buttons */}
          <div className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4">
            <Link
              href="#demo"
              className="inline-flex items-center rounded-lg bg-blue-600 px-8 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700 transition-all duration-200 hover:scale-105"
            >
              Start Free Trial
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <Link
              href="#demo"
              className="inline-flex items-center rounded-lg border border-gray-300 bg-white px-8 py-3 text-base font-semibold text-gray-700 shadow-sm hover:bg-gray-50 transition-all duration-200"
            >
              <Play className="mr-2 h-5 w-5" />
              Watch Demo
            </Link>
          </div>

          {/* Trust indicators */}
          <div className="mt-12">
            <p className="text-sm font-medium text-gray-500 mb-6">
              Trusted by developers at leading companies
            </p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {/* Company logos placeholder */}
              <div className="h-8 w-24 bg-gray-200 rounded"></div>
              <div className="h-8 w-24 bg-gray-200 rounded"></div>
              <div className="h-8 w-24 bg-gray-200 rounded"></div>
              <div className="h-8 w-24 bg-gray-200 rounded"></div>
              <div className="h-8 w-24 bg-gray-200 rounded"></div>
            </div>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3 lg:gap-16">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                    <Icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mt-4">
                    <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Hero image/demo placeholder */}
      <div className="relative mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 mt-16">
        <div className="relative rounded-2xl bg-white shadow-2xl ring-1 ring-gray-200 overflow-hidden">
          <div className="aspect-[16/10] bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Play className="w-8 h-8 text-white" />
              </div>
              <p className="text-gray-600 font-medium">Interactive Demo Coming Soon</p>
              <p className="text-sm text-gray-500 mt-2">
                See APIFlow in action with real API testing scenarios
              </p>
            </div>
          </div>
          
          {/* Floating elements for visual interest */}
          <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 opacity-90">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-xs font-medium text-gray-700">API Connected</span>
            </div>
          </div>
          
          <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3 opacity-90">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-xs font-medium text-gray-700">Tests Passing</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
