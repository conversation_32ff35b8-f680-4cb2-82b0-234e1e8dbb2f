import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import StructuredData from "@/components/seo/StructuredData";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "APIFlow - Modern API Documentation & Testing Tool",
    template: "%s | APIFlow"
  },
  description: "The modern API documentation and testing tool that developers love. Build, test, and document your APIs with ease. A powerful alternative to Postman and Apifox with 3x faster workflows.",
  keywords: [
    "API", "documentation", "testing", "Postman alternative", "Apifox alternative",
    "REST API", "GraphQL", "developer tools", "API client", "API testing tool",
    "mock server", "API collaboration", "API workflow", "developer productivity"
  ],
  authors: [{ name: "APIFlow Team", url: "https://apiflow.com" }],
  creator: "APIFlow",
  publisher: "APIFlow",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://apiflow.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "APIFlow - Modern API Documentation & Testing Tool",
    description: "The modern API documentation and testing tool that developers love. Build, test, and document your APIs with ease. A powerful alternative to Postman and Apifox.",
    url: "https://apiflow.com",
    siteName: "APIFlow",
    type: "website",
    locale: "en_US",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "APIFlow - Modern API Documentation & Testing Tool",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "APIFlow - Modern API Documentation & Testing Tool",
    description: "The modern API documentation and testing tool that developers love. Build, test, and document your APIs with ease.",
    images: ["/twitter-image.png"],
    creator: "@apiflow",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <StructuredData />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
